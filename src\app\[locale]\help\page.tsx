import { useTranslations } from 'next-intl';
import { Metadata } from 'next';
import Link from 'next/link';
import Layout from '../../components/Layout';

// 生成页面元数据
export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  const locale = params.locale;
  
  const titles = {
    zh: '帮助中心 - GamePay 游戏支付平台',
    en: 'Help Center - GamePay Game Payment Platform',
    ko: '고객지원센터 - GamePay 게임 결제 플랫폼'
  };
  
  const descriptions = {
    zh: 'GamePay帮助中心 - 常见问题、支付指南、退款政策等全面的服务支持，为您提供最优质的客户服务体验。',
    en: 'GamePay Help Center - Comprehensive service support including FAQ, payment guide, refund policy, providing you with the best customer service experience.',
    ko: 'GamePay 고객지원센터 - 자주 묻는 질문, 결제 가이드, 환불 정책 등 포괄적인 서비스 지원으로 최고의 고객 서비스 경험을 제공합니다.'
  };
  
  return {
    title: titles[locale as keyof typeof titles] || titles.zh,
    description: descriptions[locale as keyof typeof descriptions] || descriptions.zh,
    keywords: locale === 'zh' ? '帮助中心,常见问题,支付指南,退款政策,客服支持' : 
              locale === 'en' ? 'help center,FAQ,payment guide,refund policy,customer support' :
              '고객지원센터,자주 묻는 질문,결제 가이드,환불 정책,고객 지원',
    openGraph: {
      title: titles[locale as keyof typeof titles] || titles.zh,
      description: descriptions[locale as keyof typeof descriptions] || descriptions.zh,
      type: 'website',
    },
  };
}

// 图标组件
const FAQIcon = () => (
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const PaymentIcon = () => (
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>
);

const RefundIcon = () => (
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z" />
  </svg>
);

const SupportIcon = () => (
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z" />
  </svg>
);

export default function HelpPage() {
  const t = useTranslations('help');
  const locale = useTranslations().raw('nav');

  const helpSections = [
    {
      title: t('faq.title'),
      description: t('faq.subtitle'),
      icon: FAQIcon,
      href: `/help/faq`,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: t('paymentGuide.title'),
      description: t('paymentGuide.subtitle'),
      icon: PaymentIcon,
      href: `/help/payment-guide`,
      color: 'from-green-500 to-emerald-500'
    },
    {
      title: t('refundPolicy.title'),
      description: t('refundPolicy.subtitle'),
      icon: RefundIcon,
      href: `/help/refund-policy`,
      color: 'from-purple-500 to-pink-500'
    },
    {
      title: t('customerService.title'),
      description: t('customerService.description'),
      icon: SupportIcon,
      href: '#',
      color: 'from-orange-500 to-red-500',
      isCustomerService: true
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen">
        {/* 英雄区域 */}
        <section className="relative py-16 md:py-20 lg:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/10 to-indigo-900/20" />
          <div className="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10" />
          
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
                <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">
                  {t('title')}
                </span>
              </h1>
              <p className="text-lg sm:text-xl md:text-2xl text-zinc-300 mb-8 leading-relaxed">
                {t('subtitle')}
              </p>
            </div>
          </div>
        </section>

        {/* 帮助分类 */}
        <section className="py-16 md:py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
              {helpSections.map((section, index) => {
                const IconComponent = section.icon;
                const currentLocale = typeof window !== 'undefined' ? window.location.pathname.split('/')[1] : 'zh';
                
                return (
                  <div key={index} className="group">
                    {section.isCustomerService ? (
                      <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-2xl p-6 sm:p-8 h-full transition-all duration-300 hover:border-orange-500/50 hover:shadow-2xl hover:shadow-orange-500/10 hover:-translate-y-2 cursor-pointer">
                        <div className={`w-16 h-16 bg-gradient-to-br ${section.color}/20 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                          <svg className="w-8 h-8 text-orange-400 group-hover:text-orange-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z" />
                          </svg>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-4 group-hover:text-orange-300 transition-colors">
                          {section.title}
                        </h3>
                        <p className="text-zinc-400 leading-relaxed">
                          {section.description}
                        </p>
                      </div>
                    ) : (
                      <Link href={`/${currentLocale}${section.href}`}>
                        <div className="bg-gradient-to-br from-zinc-800/80 to-zinc-900/80 backdrop-blur-sm border border-zinc-700/50 rounded-2xl p-6 sm:p-8 h-full transition-all duration-300 hover:border-indigo-500/50 hover:shadow-2xl hover:shadow-indigo-500/10 hover:-translate-y-2">
                          <div className={`w-16 h-16 bg-gradient-to-br ${section.color}/20 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                            {section.title === t('faq.title') && (
                              <svg className="w-8 h-8 text-blue-400 group-hover:text-blue-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            )}
                            {section.title === t('paymentGuide.title') && (
                              <svg className="w-8 h-8 text-green-400 group-hover:text-green-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                              </svg>
                            )}
                            {section.title === t('refundPolicy.title') && (
                              <svg className="w-8 h-8 text-purple-400 group-hover:text-purple-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z" />
                              </svg>
                            )}
                            {section.title === t('customerService.title') && (
                              <svg className="w-8 h-8 text-orange-400 group-hover:text-orange-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z" />
                              </svg>
                            )}
                          </div>
                          <h3 className="text-xl font-bold text-white mb-4 group-hover:text-indigo-300 transition-colors">
                            {section.title}
                          </h3>
                          <p className="text-zinc-400 leading-relaxed">
                            {section.description}
                          </p>
                        </div>
                      </Link>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* 快速链接 */}
        <section className="py-16 md:py-20 bg-zinc-900/50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12 md:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-4">
                {t('quickLinks.title')}
              </h2>
              <p className="text-lg sm:text-xl text-zinc-300 max-w-2xl mx-auto">
                {t('quickLinks.subtitle')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
              <div className="bg-gradient-to-br from-zinc-800/60 to-zinc-900/60 backdrop-blur-sm border border-zinc-700/30 rounded-2xl p-6 md:p-8 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">{t('quickLinks.support247.title')}</h3>
                <p className="text-zinc-400 text-sm">{t('quickLinks.support247.description')}</p>
              </div>

              <div className="bg-gradient-to-br from-zinc-800/60 to-zinc-900/60 backdrop-blur-sm border border-zinc-700/30 rounded-2xl p-6 md:p-8 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">{t('quickLinks.security.title')}</h3>
                <p className="text-zinc-400 text-sm">{t('quickLinks.security.description')}</p>
              </div>

              <div className="bg-gradient-to-br from-zinc-800/60 to-zinc-900/60 backdrop-blur-sm border border-zinc-700/30 rounded-2xl p-6 md:p-8 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">{t('quickLinks.instant.title')}</h3>
                <p className="text-zinc-400 text-sm">{t('quickLinks.instant.description')}</p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
